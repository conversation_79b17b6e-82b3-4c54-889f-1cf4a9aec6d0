<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Support\Facades\DB;

class PopulateRoles extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // Original roles (commented out as they may already exist)
//        Role::insert([
//            ["name" => "contract-manager", "da_name" => "مدیر قرارداد", "can_be_acted" => 1, "can_act" => 0],
//            ["name" => "award-authority", "da_name" => "آمر اعطاء", "can_be_acted" => 1, "can_act" => 0],
//            ["name" => "specialist", "da_name" => "کارشناس", "can_be_acted" => 1, "can_act" => 0],
//            ["name" => "cpmd-manager", "da_name" => "آمر نظارت", "can_be_acted" => 1, "can_act" => 0],
//            ["name" => "company", "da_name" => "قراردادی", "can_be_acted" => 1, "can_act" => 0],
//            ["name" => "cpmd-director", "da_name" => "ریئس نظارت از پیشرفت قراردادها", "can_be_acted" => 0, "can_act" => 1],
//            ["name" => "cpmd-system-development", "da_name" => "آمریت انکشاف سیستم ها", "can_be_acted" => 0, "can_act" => 1]
//        ]);

        // Add acpms-assigner-publisher role for USM consistency
        DB::table('roles')->insertOrIgnore([
            [
                "name" => "acpms-assigner-publisher",
                "da_name" => "آمر اعطاء و نشر",
                "can_be_acted" => 1,
                "can_act" => 1,
                "created_at" => now(),
                "updated_at" => now()
            ]
        ]);
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::disableForeignKeyConstraints();

        // Remove acpms-assigner-publisher role
        DB::table('roles')->where('name', 'acpms-assigner-publisher')->delete();

//        Role::whereRaw('1=1')->delete();
        Schema::enableForeignKeyConstraints();
    }
}
