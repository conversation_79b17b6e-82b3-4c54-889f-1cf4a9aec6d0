[2025-05-28 09:42:58] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 09:43:58] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 09:44:07] production.ERROR: >>>>>>>>>>>>>>>>>>>>>>>>>\nProblem getting data from CDM\n#0 C:\0.projects\WebApp\acpms\app\Http\Controllers\ContractController.php(59): NPA\ACPMS\Http\Controllers\ContractController->loadList(Object(Requests_Response), false)
#1 [internal function]: NPA\ACPMS\Http\Controllers\ContractController->index(Object(Illuminate\Http\Request))
#2 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): call_user_func_array(Array, Array)
#3 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(45): Illuminate\Routing\Controller->callAction('index', Array)
#4 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Route.php(212): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(NPA\ACPMS\Http\Controllers\ContractController), 'index')
#5 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Route.php(169): Illuminate\Routing\Route->runController()
#6 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(658): Illuminate\Routing\Route->run()
#7 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(30): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#8 C:\0.projects\WebApp\acpms\app\Http\Middleware\Logger.php(26): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#9 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(149): NPA\ACPMS\Http\Middleware\Logger->handle(Object(Illuminate\Http\Request), Object(Closure))
#10 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#11 C:\0.projects\WebApp\acpms\app\Http\Middleware\AttachToken.php(39): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#12 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(149): NPA\ACPMS\Http\Middleware\AttachToken->handle(Object(Illuminate\Http\Request), Object(Closure))
#13 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#14 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(41): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#15 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(149): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#16 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#17 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(102): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#18 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(660): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#19 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(635): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#20 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(601): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#21 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(590): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#22 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(176): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#23 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(30): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#24 C:\0.projects\WebApp\acpms\app\Http\Middleware\Headers.php(18): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#25 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(149): NPA\ACPMS\Http\Middleware\Headers->handle(Object(Illuminate\Http\Request), Object(Closure))
#26 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#27 C:\0.projects\WebApp\acpms\app\Http\Middleware\JsonDecodeRequests.php(37): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#28 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(149): NPA\ACPMS\Http\Middleware\JsonDecodeRequests->handle(Object(Illuminate\Http\Request), Object(Closure))
#29 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#30 C:\0.projects\WebApp\acpms\vendor\fideloper\proxy\src\TrustProxies.php(56): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#31 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(149): Fideloper\Proxy\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#32 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#33 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(30): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#34 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(149): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#35 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#36 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(30): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#37 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(149): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#38 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#39 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#40 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(149): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#41 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#42 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\CheckForMaintenanceMode.php(46): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#43 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(149): Illuminate\Foundation\Http\Middleware\CheckForMaintenanceMode->handle(Object(Illuminate\Http\Request), Object(Closure))
#44 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#45 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(102): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#46 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(151): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#47 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(116): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#48 C:\0.projects\WebApp\acpms\public\index.php(48): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#49 C:\0.projects\WebApp\acpms\server.php(21): require_once('C:\\0.projects\\W...')
#50 {main}  
[2025-05-28 09:44:07] production.ERROR: \n==========================\n\n==========================\n  
[2025-05-28 09:44:58] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 09:45:58] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 09:46:58] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 09:47:58] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 09:48:58] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 09:49:58] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 09:50:58] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 09:51:58] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 09:52:58] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 09:53:58] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 09:54:58] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 09:55:58] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 09:56:58] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 09:57:58] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 09:58:59] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 09:59:59] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 10:00:59] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 10:01:59] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 10:02:59] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 11:15:55] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 11:16:55] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 11:17:55] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 11:18:55] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 11:19:55] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 11:20:55] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 11:25:00] production.ERROR: Uncaught Error: Call to undefined method NPA\ACPMS\Http\Kernel::getMiddlewareGroups() in Command line code:1
Stack trace:
#0 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught Error: Call to undefined method NPA\\ACPMS\\Http\\Kernel::getMiddlewareGroups() in Command line code:1
Stack trace:
#0 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 11:25:01] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 11:26:00] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 11:27:00] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 11:28:00] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 11:29:00] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 11:30:00] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 11:31:00] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 11:32:00] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 11:33:00] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 11:34:00] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 11:35:00] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 11:36:00] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 11:37:00] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 11:38:00] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 11:39:00] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 11:40:00] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 11:41:00] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 11:42:00] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 11:43:00] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 11:44:00] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 11:45:00] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 11:46:00] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 11:47:00] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 11:48:00] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 11:49:00] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 11:50:00] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 11:51:00] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 11:52:00] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 11:53:00] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 11:54:01] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 11:55:00] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 11:56:00] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 11:57:00] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-05-28 12:52:22] production.ERROR: Trying to access array offset on value of type null {"exception":"[object] (ErrorException(code: 0): Trying to access array offset on value of type null at C:\\0.projects\\WebApp\\acpms\\app\\Http\\Controllers\\ScorecardController.php:149)
[stacktrace]
#0 C:\\0.projects\\WebApp\\acpms\\app\\Http\\Controllers\\ScorecardController.php(149): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8, 'Trying to acces...', 'C:\\\\0.projects\\\\W...', 149, Array)
#1 [internal function]: NPA\\ACPMS\\Http\\Controllers\\ScorecardController->contractProgress(Object(Illuminate\\Http\\Request))
#2 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): call_user_func_array(Array, Array)
#3 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('contractProgres...', Array)
#4 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(NPA\\ACPMS\\Http\\Controllers\\ScorecardController), 'contractProgres...')
#5 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(169): Illuminate\\Routing\\Route->runController()
#6 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(658): Illuminate\\Routing\\Route->run()
#7 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(30): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\0.projects\\WebApp\\acpms\\app\\Http\\Middleware\\Logger.php(26): Illuminate\\Routing\\Pipeline->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(149): NPA\\ACPMS\\Http\\Middleware\\Logger->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(53): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\0.projects\\WebApp\\acpms\\app\\Http\\Middleware\\AttachToken.php(39): Illuminate\\Routing\\Pipeline->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(149): NPA\\ACPMS\\Http\\Middleware\\AttachToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(53): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(41): Illuminate\\Routing\\Pipeline->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(149): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(53): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(102): Illuminate\\Routing\\Pipeline->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(660): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(635): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(601): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(590): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(30): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\0.projects\\WebApp\\acpms\\app\\Http\\Middleware\\Headers.php(18): Illuminate\\Routing\\Pipeline->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(149): NPA\\ACPMS\\Http\\Middleware\\Headers->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(53): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\0.projects\\WebApp\\acpms\\app\\Http\\Middleware\\JsonDecodeRequests.php(37): Illuminate\\Routing\\Pipeline->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(149): NPA\\ACPMS\\Http\\Middleware\\JsonDecodeRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(53): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\0.projects\\WebApp\\acpms\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(56): Illuminate\\Routing\\Pipeline->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(149): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(53): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(30): Illuminate\\Routing\\Pipeline->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(149): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(53): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(30): Illuminate\\Routing\\Pipeline->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(149): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(53): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Routing\\Pipeline->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(149): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(53): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(46): Illuminate\\Routing\\Pipeline->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(149): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(53): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(102): Illuminate\\Routing\\Pipeline->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(151): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#47 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(116): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#48 C:\\0.projects\\WebApp\\acpms\\public\\index.php(48): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#49 C:\\0.projects\\WebApp\\acpms\\server.php(21): require_once('C:\\\\0.projects\\\\W...')
#50 {main}
"} 
[2025-05-28 12:52:22] production.ERROR: >>>>>>>>> API Specific: ErrorException\nTrying to access array offset on value of type null  
[2025-05-28 12:52:25] production.ERROR: Trying to access array offset on value of type null {"exception":"[object] (ErrorException(code: 0): Trying to access array offset on value of type null at C:\\0.projects\\WebApp\\acpms\\app\\Http\\Controllers\\ContractProgress\\ContractProgressPaymentController.php:400)
[stacktrace]
#0 C:\\0.projects\\WebApp\\acpms\\app\\Http\\Controllers\\ContractProgress\\ContractProgressPaymentController.php(400): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(8, 'Trying to acces...', 'C:\\\\0.projects\\\\W...', 400, Array)
#1 [internal function]: NPA\\ACPMS\\Http\\Controllers\\ContractProgress\\ContractProgressPaymentController->isAdvancePaymentApplicable(Object(Illuminate\\Http\\Request), '5459')
#2 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): call_user_func_array(Array, Array)
#3 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('isAdvancePaymen...', Array)
#4 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(212): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(NPA\\ACPMS\\Http\\Controllers\\ContractProgress\\ContractProgressPaymentController), 'isAdvancePaymen...')
#5 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(169): Illuminate\\Routing\\Route->runController()
#6 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(658): Illuminate\\Routing\\Route->run()
#7 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(30): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#8 C:\\0.projects\\WebApp\\acpms\\app\\Http\\Middleware\\Logger.php(26): Illuminate\\Routing\\Pipeline->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(149): NPA\\ACPMS\\Http\\Middleware\\Logger->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(53): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 C:\\0.projects\\WebApp\\acpms\\app\\Http\\Middleware\\AttachToken.php(39): Illuminate\\Routing\\Pipeline->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(149): NPA\\ACPMS\\Http\\Middleware\\AttachToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(53): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(41): Illuminate\\Routing\\Pipeline->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#15 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(149): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(53): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(102): Illuminate\\Routing\\Pipeline->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(660): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(635): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(601): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(590): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(176): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(30): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\0.projects\\WebApp\\acpms\\app\\Http\\Middleware\\Headers.php(18): Illuminate\\Routing\\Pipeline->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(149): NPA\\ACPMS\\Http\\Middleware\\Headers->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(53): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\0.projects\\WebApp\\acpms\\app\\Http\\Middleware\\JsonDecodeRequests.php(37): Illuminate\\Routing\\Pipeline->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(149): NPA\\ACPMS\\Http\\Middleware\\JsonDecodeRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(53): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\0.projects\\WebApp\\acpms\\vendor\\fideloper\\proxy\\src\\TrustProxies.php(56): Illuminate\\Routing\\Pipeline->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(149): Fideloper\\Proxy\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(53): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(30): Illuminate\\Routing\\Pipeline->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(149): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(53): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(30): Illuminate\\Routing\\Pipeline->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(149): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(53): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Routing\\Pipeline->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(149): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(53): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode.php(46): Illuminate\\Routing\\Pipeline->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(149): Illuminate\\Foundation\\Http\\Middleware\\CheckForMaintenanceMode->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Pipeline.php(53): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(102): Illuminate\\Routing\\Pipeline->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(151): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#47 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(116): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#48 C:\\0.projects\\WebApp\\acpms\\public\\index.php(48): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#49 C:\\0.projects\\WebApp\\acpms\\server.php(21): require_once('C:\\\\0.projects\\\\W...')
#50 {main}
"} 
[2025-05-28 12:52:25] production.ERROR: >>>>>>>>> API Specific: ErrorException\nTrying to access array offset on value of type null  
[2025-05-28 12:55:00] production.INFO: -------------------------------------------------------------  
[2025-05-28 12:55:00] production.INFO: Before the try and catch block  
[2025-05-28 12:55:00] production.INFO: baseUrl :http://localhost:8009  
[2025-05-28 12:55:00] production.INFO: path :/api/contract-detail  
[2025-05-28 12:55:00] production.INFO: -------------------------------------------------------------  
[2025-05-28 12:55:20] production.INFO: exception 'Aws\S3\Exception\S3Exception' with message 'Error executing "PutObject" on "https://test-acpms.s3.ap-northeast-2.amazonaws.com/api/contract-detail/**********-125886-launcher_icon.old-removebg-preview.png"; AWS HTTP error: Client error: `PUT https://test-acpms.s3.ap-northeast-2.amazonaws.com/api/contract-detail/**********-125886-launcher_icon.old-removebg-preview.png` resulted in a `403 Forbidden` response:
<?xml version="1.0" encoding="UTF-8"?>
<Error><Code>InvalidAccessKeyId</Code><Message>The AWS Access Key Id you provided (truncated...)
 InvalidAccessKeyId (client): The AWS Access Key Id you provided does not exist in our records. - <?xml version="1.0" encoding="UTF-8"?>
<Error><Code>InvalidAccessKeyId</Code><Message>The AWS Access Key Id you provided does not exist in our records.</Message><AWSAccessKeyId>AKIAIQDJ3TVG33XVUH3Q</AWSAccessKeyId><RequestId>YZCRE45ZBYQVA79G</RequestId><HostId>WmekN+9jwDFAb0LWOhaeMxKD8eLYbaSInMuXIQilegEDWiqvSvOXuV9ClrlNzEPJSGCaxf46r7PAxFjqlCK5Bz/ZNWUow320</HostId></Error>'

GuzzleHttp\Exception\ClientException: Client error: `PUT https://test-acpms.s3.ap-northeast-2.amazonaws.com/api/contract-detail/**********-125886-launcher_icon.old-removebg-preview.png` resulted in a `403 Forbidden` response:
<?xml version="1.0" encoding="UTF-8"?>
<Error><Code>InvalidAccessKeyId</Code><Message>The AWS Access Key Id you provided (truncated...)
 in C:\0.projects\WebApp\acpms\vendor\guzzlehttp\guzzle\src\Exception\RequestException.php:113
Stack trace:
#0 C:\0.projects\WebApp\acpms\vendor\guzzlehttp\guzzle\src\Middleware.php(65): GuzzleHttp\Exception\RequestException::create(Object(GuzzleHttp\Psr7\Request), Object(GuzzleHttp\Psr7\Response))
#1 C:\0.projects\WebApp\acpms\vendor\guzzlehttp\promises\src\Promise.php(204): GuzzleHttp\Middleware::GuzzleHttp\{closure}(Object(GuzzleHttp\Psr7\Response))
#2 C:\0.projects\WebApp\acpms\vendor\guzzlehttp\promises\src\Promise.php(153): GuzzleHttp\Promise\Promise::callHandler(1, Object(GuzzleHttp\Psr7\Response), NULL)
#3 C:\0.projects\WebApp\acpms\vendor\guzzlehttp\promises\src\TaskQueue.php(48): GuzzleHttp\Promise\Promise::GuzzleHttp\Promise\{closure}()
#4 C:\0.projects\WebApp\acpms\vendor\guzzlehttp\guzzle\src\Handler\CurlMultiHandler.php(118): GuzzleHttp\Promise\TaskQueue->run()
#5 C:\0.projects\WebApp\acpms\vendor\guzzlehttp\guzzle\src\Handler\CurlMultiHandler.php(145): GuzzleHttp\Handler\CurlMultiHandler->tick()
#6 C:\0.projects\WebApp\acpms\vendor\guzzlehttp\promises\src\Promise.php(248): GuzzleHttp\Handler\CurlMultiHandler->execute(true)
#7 C:\0.projects\WebApp\acpms\vendor\guzzlehttp\promises\src\Promise.php(224): GuzzleHttp\Promise\Promise->invokeWaitFn()
#8 C:\0.projects\WebApp\acpms\vendor\guzzlehttp\promises\src\Promise.php(269): GuzzleHttp\Promise\Promise->waitIfPending()
#9 C:\0.projects\WebApp\acpms\vendor\guzzlehttp\promises\src\Promise.php(226): GuzzleHttp\Promise\Promise->invokeWaitList()
#10 C:\0.projects\WebApp\acpms\vendor\guzzlehttp\promises\src\Promise.php(269): GuzzleHttp\Promise\Promise->waitIfPending()
#11 C:\0.projects\WebApp\acpms\vendor\guzzlehttp\promises\src\Promise.php(226): GuzzleHttp\Promise\Promise->invokeWaitList()
#12 C:\0.projects\WebApp\acpms\vendor\guzzlehttp\promises\src\Promise.php(62): GuzzleHttp\Promise\Promise->waitIfPending()
#13 C:\0.projects\WebApp\acpms\vendor\aws\aws-sdk-php\src\S3\S3ClientTrait.php(35): GuzzleHttp\Promise\Promise->wait()
#14 C:\0.projects\WebApp\acpms\vendor\league\flysystem-aws-s3-v3\src\AwsS3Adapter.php(607): Aws\S3\S3Client->upload('test-acpms', 'api/contract-de...', Resource id #502, 'public-read', Array)
#15 C:\0.projects\WebApp\acpms\vendor\league\flysystem-aws-s3-v3\src\AwsS3Adapter.php(392): League\Flysystem\AwsS3v3\AwsS3Adapter->upload('api/contract-de...', Resource id #502, Object(League\Flysystem\Config))
#16 C:\0.projects\WebApp\acpms\vendor\league\flysystem\src\Filesystem.php(123): League\Flysystem\AwsS3v3\AwsS3Adapter->writeStream('api/contract-de...', Resource id #502, Object(League\Flysystem\Config))
#17 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Filesystem\FilesystemAdapter.php(179): League\Flysystem\Filesystem->putStream('api/contract-de...', Resource id #502, Object(League\Flysystem\Config))
#18 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Filesystem\FilesystemAdapter.php(213): Illuminate\Filesystem\FilesystemAdapter->put('api/contract-de...', Resource id #502, Array)
#19 C:\0.projects\WebApp\acpms\app\Http\Controllers\AttachmentController.php(181): Illuminate\Filesystem\FilesystemAdapter->putFileAs('api/contract-de...', Object(Illuminate\Http\UploadedFile), '**********-1258...', 'public')
#20 C:\0.projects\WebApp\acpms\app\Http\Controllers\ContractPlanning\ContractDetailsController.php(117): NPA\ACPMS\Http\Controllers\AttachmentController::saveFile(Object(Illuminate\Http\Request), Object(NPA\ACPMS\Models\ContractDetails), 'bidder_conflict...', Array)
#21 [internal function]: NPA\ACPMS\Http\Controllers\ContractPlanning\ContractDetailsController->store(Object(Illuminate\Http\Request))
#22 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): call_user_func_array(Array, Array)
#23 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(45): Illuminate\Routing\Controller->callAction('store', Array)
#24 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Route.php(212): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(NPA\ACPMS\Http\Controllers\ContractPlanning\ContractDetailsController), 'store')
#25 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Route.php(169): Illuminate\Routing\Route->runController()
#26 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(658): Illuminate\Routing\Route->run()
#27 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(30): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#28 C:\0.projects\WebApp\acpms\app\Http\Middleware\Logger.php(26): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#29 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(149): NPA\ACPMS\Http\Middleware\Logger->handle(Object(Illuminate\Http\Request), Object(Closure))
#30 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#31 C:\0.projects\WebApp\acpms\app\Http\Middleware\AttachToken.php(39): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#32 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(149): NPA\ACPMS\Http\Middleware\AttachToken->handle(Object(Illuminate\Http\Request), Object(Closure))
#33 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#34 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(41): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#35 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(149): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#36 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#37 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(102): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#38 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(660): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#39 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(635): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#40 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(601): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#41 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(590): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#42 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(176): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#43 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(30): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#44 C:\0.projects\WebApp\acpms\app\Http\Middleware\Headers.php(18): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#45 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(149): NPA\ACPMS\Http\Middleware\Headers->handle(Object(Illuminate\Http\Request), Object(Closure))
#46 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#47 C:\0.projects\WebApp\acpms\app\Http\Middleware\JsonDecodeRequests.php(35): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#48 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(149): NPA\ACPMS\Http\Middleware\JsonDecodeRequests->handle(Object(Illuminate\Http\Request), Object(Closure))
#49 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#50 C:\0.projects\WebApp\acpms\vendor\fideloper\proxy\src\TrustProxies.php(56): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#51 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(149): Fideloper\Proxy\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#52 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#53 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(30): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#54 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(149): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#55 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#56 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(30): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#57 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(149): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#58 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#59 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#60 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(149): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#61 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#62 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\CheckForMaintenanceMode.php(46): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#63 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(149): Illuminate\Foundation\Http\Middleware\CheckForMaintenanceMode->handle(Object(Illuminate\Http\Request), Object(Closure))
#64 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#65 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(102): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#66 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(151): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#67 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(116): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#68 C:\0.projects\WebApp\acpms\public\index.php(48): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#69 C:\0.projects\WebApp\acpms\server.php(21): require_once('C:\\0.projects\\W...')
#70 {main}

Next Aws\S3\Exception\S3Exception: Error executing "PutObject" on "https://test-acpms.s3.ap-northeast-2.amazonaws.com/api/contract-detail/**********-125886-launcher_icon.old-removebg-preview.png"; AWS HTTP error: Client error: `PUT https://test-acpms.s3.ap-northeast-2.amazonaws.com/api/contract-detail/**********-125886-launcher_icon.old-removebg-preview.png` resulted in a `403 Forbidden` response:
<?xml version="1.0" encoding="UTF-8"?>
<Error><Code>InvalidAccessKeyId</Code><Message>The AWS Access Key Id you provided (truncated...)
 InvalidAccessKeyId (client): The AWS Access Key Id you provided does not exist in our records. - <?xml version="1.0" encoding="UTF-8"?>
<Error><Code>InvalidAccessKeyId</Code><Message>The AWS Access Key Id you provided does not exist in our records.</Message><AWSAccessKeyId>AKIAIQDJ3TVG33XVUH3Q</AWSAccessKeyId><RequestId>YZCRE45ZBYQVA79G</RequestId><HostId>WmekN+9jwDFAb0LWOhaeMxKD8eLYbaSInMuXIQilegEDWiqvSvOXuV9ClrlNzEPJSGCaxf46r7PAxFjqlCK5Bz/ZNWUow320</HostId></Error> in C:\0.projects\WebApp\acpms\vendor\aws\aws-sdk-php\src\WrappedHttpHandler.php:195
Stack trace:
#0 C:\0.projects\WebApp\acpms\vendor\aws\aws-sdk-php\src\WrappedHttpHandler.php(97): Aws\WrappedHttpHandler->parseError(Array, Object(GuzzleHttp\Psr7\Request), Object(Aws\Command), Array)
#1 C:\0.projects\WebApp\acpms\vendor\guzzlehttp\promises\src\Promise.php(204): Aws\WrappedHttpHandler->Aws\{closure}(Array)
#2 C:\0.projects\WebApp\acpms\vendor\guzzlehttp\promises\src\Promise.php(169): GuzzleHttp\Promise\Promise::callHandler(2, Array, NULL)
#3 C:\0.projects\WebApp\acpms\vendor\guzzlehttp\promises\src\RejectedPromise.php(42): GuzzleHttp\Promise\Promise::GuzzleHttp\Promise\{closure}(Array)
#4 C:\0.projects\WebApp\acpms\vendor\guzzlehttp\promises\src\TaskQueue.php(48): GuzzleHttp\Promise\RejectedPromise::GuzzleHttp\Promise\{closure}()
#5 C:\0.projects\WebApp\acpms\vendor\guzzlehttp\guzzle\src\Handler\CurlMultiHandler.php(118): GuzzleHttp\Promise\TaskQueue->run()
#6 C:\0.projects\WebApp\acpms\vendor\guzzlehttp\guzzle\src\Handler\CurlMultiHandler.php(145): GuzzleHttp\Handler\CurlMultiHandler->tick()
#7 C:\0.projects\WebApp\acpms\vendor\guzzlehttp\promises\src\Promise.php(248): GuzzleHttp\Handler\CurlMultiHandler->execute(true)
#8 C:\0.projects\WebApp\acpms\vendor\guzzlehttp\promises\src\Promise.php(224): GuzzleHttp\Promise\Promise->invokeWaitFn()
#9 C:\0.projects\WebApp\acpms\vendor\guzzlehttp\promises\src\Promise.php(269): GuzzleHttp\Promise\Promise->waitIfPending()
#10 C:\0.projects\WebApp\acpms\vendor\guzzlehttp\promises\src\Promise.php(226): GuzzleHttp\Promise\Promise->invokeWaitList()
#11 C:\0.projects\WebApp\acpms\vendor\guzzlehttp\promises\src\Promise.php(269): GuzzleHttp\Promise\Promise->waitIfPending()
#12 C:\0.projects\WebApp\acpms\vendor\guzzlehttp\promises\src\Promise.php(226): GuzzleHttp\Promise\Promise->invokeWaitList()
#13 C:\0.projects\WebApp\acpms\vendor\guzzlehttp\promises\src\Promise.php(62): GuzzleHttp\Promise\Promise->waitIfPending()
#14 C:\0.projects\WebApp\acpms\vendor\aws\aws-sdk-php\src\S3\S3ClientTrait.php(35): GuzzleHttp\Promise\Promise->wait()
#15 C:\0.projects\WebApp\acpms\vendor\league\flysystem-aws-s3-v3\src\AwsS3Adapter.php(607): Aws\S3\S3Client->upload('test-acpms', 'api/contract-de...', Resource id #502, 'public-read', Array)
#16 C:\0.projects\WebApp\acpms\vendor\league\flysystem-aws-s3-v3\src\AwsS3Adapter.php(392): League\Flysystem\AwsS3v3\AwsS3Adapter->upload('api/contract-de...', Resource id #502, Object(League\Flysystem\Config))
#17 C:\0.projects\WebApp\acpms\vendor\league\flysystem\src\Filesystem.php(123): League\Flysystem\AwsS3v3\AwsS3Adapter->writeStream('api/contract-de...', Resource id #502, Object(League\Flysystem\Config))
#18 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Filesystem\FilesystemAdapter.php(179): League\Flysystem\Filesystem->putStream('api/contract-de...', Resource id #502, Object(League\Flysystem\Config))
#19 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Filesystem\FilesystemAdapter.php(213): Illuminate\Filesystem\FilesystemAdapter->put('api/contract-de...', Resource id #502, Array)
#20 C:\0.projects\WebApp\acpms\app\Http\Controllers\AttachmentController.php(181): Illuminate\Filesystem\FilesystemAdapter->putFileAs('api/contract-de...', Object(Illuminate\Http\UploadedFile), '**********-1258...', 'public')
#21 C:\0.projects\WebApp\acpms\app\Http\Controllers\ContractPlanning\ContractDetailsController.php(117): NPA\ACPMS\Http\Controllers\AttachmentController::saveFile(Object(Illuminate\Http\Request), Object(NPA\ACPMS\Models\ContractDetails), 'bidder_conflict...', Array)
#22 [internal function]: NPA\ACPMS\Http\Controllers\ContractPlanning\ContractDetailsController->store(Object(Illuminate\Http\Request))
#23 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): call_user_func_array(Array, Array)
#24 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(45): Illuminate\Routing\Controller->callAction('store', Array)
#25 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Route.php(212): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(NPA\ACPMS\Http\Controllers\ContractPlanning\ContractDetailsController), 'store')
#26 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Route.php(169): Illuminate\Routing\Route->runController()
#27 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(658): Illuminate\Routing\Route->run()
#28 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(30): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#29 C:\0.projects\WebApp\acpms\app\Http\Middleware\Logger.php(26): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#30 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(149): NPA\ACPMS\Http\Middleware\Logger->handle(Object(Illuminate\Http\Request), Object(Closure))
#31 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#32 C:\0.projects\WebApp\acpms\app\Http\Middleware\AttachToken.php(39): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#33 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(149): NPA\ACPMS\Http\Middleware\AttachToken->handle(Object(Illuminate\Http\Request), Object(Closure))
#34 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#35 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(41): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#36 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(149): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#37 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#38 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(102): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#39 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(660): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#40 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(635): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#41 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(601): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#42 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(590): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#43 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(176): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#44 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(30): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#45 C:\0.projects\WebApp\acpms\app\Http\Middleware\Headers.php(18): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#46 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(149): NPA\ACPMS\Http\Middleware\Headers->handle(Object(Illuminate\Http\Request), Object(Closure))
#47 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#48 C:\0.projects\WebApp\acpms\app\Http\Middleware\JsonDecodeRequests.php(35): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#49 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(149): NPA\ACPMS\Http\Middleware\JsonDecodeRequests->handle(Object(Illuminate\Http\Request), Object(Closure))
#50 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#51 C:\0.projects\WebApp\acpms\vendor\fideloper\proxy\src\TrustProxies.php(56): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#52 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(149): Fideloper\Proxy\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#53 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#54 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(30): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#55 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(149): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#56 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#57 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(30): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#58 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(149): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#59 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#60 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#61 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(149): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#62 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#63 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\CheckForMaintenanceMode.php(46): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#64 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(149): Illuminate\Foundation\Http\Middleware\CheckForMaintenanceMode->handle(Object(Illuminate\Http\Request), Object(Closure))
#65 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#66 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(102): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#67 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(151): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#68 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(116): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#69 C:\0.projects\WebApp\acpms\public\index.php(48): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#70 C:\0.projects\WebApp\acpms\server.php(21): require_once('C:\\0.projects\\W...')
#71 {main}  
[2025-05-28 12:55:20] production.ERROR: >>>>>>>>>>>>>>>>>>>>>>>>>\nError executing "PutObject" on "https://test-acpms.s3.ap-northeast-2.amazonaws.com/api/contract-detail/**********-125886-launcher_icon.old-removebg-preview.png"; AWS HTTP error: Client error: `PUT https://test-acpms.s3.ap-northeast-2.amazonaws.com/api/contract-detail/**********-125886-launcher_icon.old-removebg-preview.png` resulted in a `403 Forbidden` response:
<?xml version="1.0" encoding="UTF-8"?>
<Error><Code>InvalidAccessKeyId</Code><Message>The AWS Access Key Id you provided (truncated...)
 InvalidAccessKeyId (client): The AWS Access Key Id you provided does not exist in our records. - <?xml version="1.0" encoding="UTF-8"?>
<Error><Code>InvalidAccessKeyId</Code><Message>The AWS Access Key Id you provided does not exist in our records.</Message><AWSAccessKeyId>AKIAIQDJ3TVG33XVUH3Q</AWSAccessKeyId><RequestId>YZCRE45ZBYQVA79G</RequestId><HostId>WmekN+9jwDFAb0LWOhaeMxKD8eLYbaSInMuXIQilegEDWiqvSvOXuV9ClrlNzEPJSGCaxf46r7PAxFjqlCK5Bz/ZNWUow320</HostId></Error>\n#0 C:\0.projects\WebApp\acpms\vendor\aws\aws-sdk-php\src\WrappedHttpHandler.php(97): Aws\WrappedHttpHandler->parseError(Array, Object(GuzzleHttp\Psr7\Request), Object(Aws\Command), Array)
#1 C:\0.projects\WebApp\acpms\vendor\guzzlehttp\promises\src\Promise.php(204): Aws\WrappedHttpHandler->Aws\{closure}(Array)
#2 C:\0.projects\WebApp\acpms\vendor\guzzlehttp\promises\src\Promise.php(169): GuzzleHttp\Promise\Promise::callHandler(2, Array, NULL)
#3 C:\0.projects\WebApp\acpms\vendor\guzzlehttp\promises\src\RejectedPromise.php(42): GuzzleHttp\Promise\Promise::GuzzleHttp\Promise\{closure}(Array)
#4 C:\0.projects\WebApp\acpms\vendor\guzzlehttp\promises\src\TaskQueue.php(48): GuzzleHttp\Promise\RejectedPromise::GuzzleHttp\Promise\{closure}()
#5 C:\0.projects\WebApp\acpms\vendor\guzzlehttp\guzzle\src\Handler\CurlMultiHandler.php(118): GuzzleHttp\Promise\TaskQueue->run()
#6 C:\0.projects\WebApp\acpms\vendor\guzzlehttp\guzzle\src\Handler\CurlMultiHandler.php(145): GuzzleHttp\Handler\CurlMultiHandler->tick()
#7 C:\0.projects\WebApp\acpms\vendor\guzzlehttp\promises\src\Promise.php(248): GuzzleHttp\Handler\CurlMultiHandler->execute(true)
#8 C:\0.projects\WebApp\acpms\vendor\guzzlehttp\promises\src\Promise.php(224): GuzzleHttp\Promise\Promise->invokeWaitFn()
#9 C:\0.projects\WebApp\acpms\vendor\guzzlehttp\promises\src\Promise.php(269): GuzzleHttp\Promise\Promise->waitIfPending()
#10 C:\0.projects\WebApp\acpms\vendor\guzzlehttp\promises\src\Promise.php(226): GuzzleHttp\Promise\Promise->invokeWaitList()
#11 C:\0.projects\WebApp\acpms\vendor\guzzlehttp\promises\src\Promise.php(269): GuzzleHttp\Promise\Promise->waitIfPending()
#12 C:\0.projects\WebApp\acpms\vendor\guzzlehttp\promises\src\Promise.php(226): GuzzleHttp\Promise\Promise->invokeWaitList()
#13 C:\0.projects\WebApp\acpms\vendor\guzzlehttp\promises\src\Promise.php(62): GuzzleHttp\Promise\Promise->waitIfPending()
#14 C:\0.projects\WebApp\acpms\vendor\aws\aws-sdk-php\src\S3\S3ClientTrait.php(35): GuzzleHttp\Promise\Promise->wait()
#15 C:\0.projects\WebApp\acpms\vendor\league\flysystem-aws-s3-v3\src\AwsS3Adapter.php(607): Aws\S3\S3Client->upload('test-acpms', 'api/contract-de...', Resource id #502, 'public-read', Array)
#16 C:\0.projects\WebApp\acpms\vendor\league\flysystem-aws-s3-v3\src\AwsS3Adapter.php(392): League\Flysystem\AwsS3v3\AwsS3Adapter->upload('api/contract-de...', Resource id #502, Object(League\Flysystem\Config))
#17 C:\0.projects\WebApp\acpms\vendor\league\flysystem\src\Filesystem.php(123): League\Flysystem\AwsS3v3\AwsS3Adapter->writeStream('api/contract-de...', Resource id #502, Object(League\Flysystem\Config))
#18 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Filesystem\FilesystemAdapter.php(179): League\Flysystem\Filesystem->putStream('api/contract-de...', Resource id #502, Object(League\Flysystem\Config))
#19 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Filesystem\FilesystemAdapter.php(213): Illuminate\Filesystem\FilesystemAdapter->put('api/contract-de...', Resource id #502, Array)
#20 C:\0.projects\WebApp\acpms\app\Http\Controllers\AttachmentController.php(181): Illuminate\Filesystem\FilesystemAdapter->putFileAs('api/contract-de...', Object(Illuminate\Http\UploadedFile), '**********-1258...', 'public')
#21 C:\0.projects\WebApp\acpms\app\Http\Controllers\ContractPlanning\ContractDetailsController.php(117): NPA\ACPMS\Http\Controllers\AttachmentController::saveFile(Object(Illuminate\Http\Request), Object(NPA\ACPMS\Models\ContractDetails), 'bidder_conflict...', Array)
#22 [internal function]: NPA\ACPMS\Http\Controllers\ContractPlanning\ContractDetailsController->store(Object(Illuminate\Http\Request))
#23 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Controller.php(54): call_user_func_array(Array, Array)
#24 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\ControllerDispatcher.php(45): Illuminate\Routing\Controller->callAction('store', Array)
#25 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Route.php(212): Illuminate\Routing\ControllerDispatcher->dispatch(Object(Illuminate\Routing\Route), Object(NPA\ACPMS\Http\Controllers\ContractPlanning\ContractDetailsController), 'store')
#26 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Route.php(169): Illuminate\Routing\Route->runController()
#27 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(658): Illuminate\Routing\Route->run()
#28 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(30): Illuminate\Routing\Router->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#29 C:\0.projects\WebApp\acpms\app\Http\Middleware\Logger.php(26): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#30 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(149): NPA\ACPMS\Http\Middleware\Logger->handle(Object(Illuminate\Http\Request), Object(Closure))
#31 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#32 C:\0.projects\WebApp\acpms\app\Http\Middleware\AttachToken.php(39): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#33 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(149): NPA\ACPMS\Http\Middleware\AttachToken->handle(Object(Illuminate\Http\Request), Object(Closure))
#34 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#35 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Middleware\SubstituteBindings.php(41): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#36 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(149): Illuminate\Routing\Middleware\SubstituteBindings->handle(Object(Illuminate\Http\Request), Object(Closure))
#37 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#38 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(102): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#39 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(660): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#40 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(635): Illuminate\Routing\Router->runRouteWithinStack(Object(Illuminate\Routing\Route), Object(Illuminate\Http\Request))
#41 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(601): Illuminate\Routing\Router->runRoute(Object(Illuminate\Http\Request), Object(Illuminate\Routing\Route))
#42 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Router.php(590): Illuminate\Routing\Router->dispatchToRoute(Object(Illuminate\Http\Request))
#43 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(176): Illuminate\Routing\Router->dispatch(Object(Illuminate\Http\Request))
#44 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(30): Illuminate\Foundation\Http\Kernel->Illuminate\Foundation\Http\{closure}(Object(Illuminate\Http\Request))
#45 C:\0.projects\WebApp\acpms\app\Http\Middleware\Headers.php(18): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#46 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(149): NPA\ACPMS\Http\Middleware\Headers->handle(Object(Illuminate\Http\Request), Object(Closure))
#47 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#48 C:\0.projects\WebApp\acpms\app\Http\Middleware\JsonDecodeRequests.php(35): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#49 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(149): NPA\ACPMS\Http\Middleware\JsonDecodeRequests->handle(Object(Illuminate\Http\Request), Object(Closure))
#50 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#51 C:\0.projects\WebApp\acpms\vendor\fideloper\proxy\src\TrustProxies.php(56): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#52 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(149): Fideloper\Proxy\TrustProxies->handle(Object(Illuminate\Http\Request), Object(Closure))
#53 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#54 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(30): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#55 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(149): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#56 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#57 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\TransformsRequest.php(30): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#58 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(149): Illuminate\Foundation\Http\Middleware\TransformsRequest->handle(Object(Illuminate\Http\Request), Object(Closure))
#59 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#60 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\ValidatePostSize.php(27): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#61 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(149): Illuminate\Foundation\Http\Middleware\ValidatePostSize->handle(Object(Illuminate\Http\Request), Object(Closure))
#62 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#63 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Middleware\CheckForMaintenanceMode.php(46): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#64 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(149): Illuminate\Foundation\Http\Middleware\CheckForMaintenanceMode->handle(Object(Illuminate\Http\Request), Object(Closure))
#65 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Routing\Pipeline.php(53): Illuminate\Pipeline\Pipeline->Illuminate\Pipeline\{closure}(Object(Illuminate\Http\Request))
#66 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Pipeline\Pipeline.php(102): Illuminate\Routing\Pipeline->Illuminate\Routing\{closure}(Object(Illuminate\Http\Request))
#67 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(151): Illuminate\Pipeline\Pipeline->then(Object(Closure))
#68 C:\0.projects\WebApp\acpms\vendor\laravel\framework\src\Illuminate\Foundation\Http\Kernel.php(116): Illuminate\Foundation\Http\Kernel->sendRequestThroughRouter(Object(Illuminate\Http\Request))
#69 C:\0.projects\WebApp\acpms\public\index.php(48): Illuminate\Foundation\Http\Kernel->handle(Object(Illuminate\Http\Request))
#70 C:\0.projects\WebApp\acpms\server.php(21): require_once('C:\\0.projects\\W...')
#71 {main}  
[2025-05-28 12:55:20] production.ERROR: \n==========================\n\n==========================\n  
[2025-06-02 09:06:04] production.ERROR: Command "servre" is not defined.

Did you mean this?
    serve {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"servre\" is not defined.

Did you mean this?
    serve at C:\\0.projects\\WebApp\\acpms\\vendor\\symfony\\console\\Application.php:651)
[stacktrace]
#0 C:\\0.projects\\WebApp\\acpms\\vendor\\symfony\\console\\Application.php(236): Symfony\\Component\\Console\\Application->find('servre')
#1 C:\\0.projects\\WebApp\\acpms\\vendor\\symfony\\console\\Application.php(148): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#2 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(88): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(121): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\0.projects\\WebApp\\acpms\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 {main}
"} 
[2025-06-02 09:06:18] production.ERROR: Command "servre" is not defined.

Did you mean this?
    serve {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"servre\" is not defined.

Did you mean this?
    serve at C:\\0.projects\\WebApp\\acpms\\vendor\\symfony\\console\\Application.php:651)
[stacktrace]
#0 C:\\0.projects\\WebApp\\acpms\\vendor\\symfony\\console\\Application.php(236): Symfony\\Component\\Console\\Application->find('servre')
#1 C:\\0.projects\\WebApp\\acpms\\vendor\\symfony\\console\\Application.php(148): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#2 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(88): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\0.projects\\WebApp\\acpms\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(121): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\0.projects\\WebApp\\acpms\\artisan(37): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 {main}
"} 
[2025-06-02 09:27:38] production.ERROR: Uncaught Error: Call to undefined method NPA\ACPMS\Http\Kernel::getMiddlewareGroups() in Command line code:1
Stack trace:
#0 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught Error: Call to undefined method NPA\\ACPMS\\Http\\Kernel::getMiddlewareGroups() in Command line code:1
Stack trace:
#0 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
rrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 09:28:34] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 09:29:34] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 09:30:34] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 09:31:34] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 09:32:34] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 09:33:34] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 09:34:34] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 09:35:34] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 09:36:34] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 09:37:34] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 09:38:34] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 09:39:34] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 09:40:34] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 09:41:34] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 09:42:34] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 09:43:39] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 09:44:34] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 09:45:34] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 09:46:34] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 09:47:34] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 09:48:34] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 09:49:34] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 09:50:34] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 09:51:34] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 09:52:35] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 09:53:34] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 09:54:35] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 09:55:34] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 09:56:35] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 09:57:35] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 09:58:35] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 09:59:35] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 10:00:35] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 10:01:35] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 10:02:35] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 10:03:35] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 10:04:35] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 10:05:35] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 10:06:35] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 10:07:35] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 10:08:35] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 10:09:35] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 10:10:35] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 10:11:35] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 10:12:35] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 10:13:35] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 10:14:35] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 10:15:35] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 10:16:35] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 10:17:35] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 10:18:35] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 10:19:35] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 10:20:35] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 10:21:35] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 10:22:35] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 10:23:35] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 10:24:35] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 10:25:35] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 10:26:35] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 10:27:35] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 10:28:35] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 10:29:35] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 10:30:35] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 10:31:35] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 10:32:35] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 10:33:35] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 10:34:35] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 10:35:35] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 10:36:35] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 10:37:35] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-06-02 10:38:35] production.ERROR: Uncaught ReflectionException: Class NPA\ACPMS\Policies\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\ACPMS\\Polic...')
#1 [internal function]: {closure}('NPA\\ACPMS\\Polic...', 'NPA\\ACPMS\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\Debug\\Exception\\FatalErrorException(code: 1): Uncaught ReflectionException: Class NPA\\ACPMS\\Policies\\ModelPolicy does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('NPA\\\\ACPMS\\\\Polic...')
#1 [internal function]: {closure}('NPA\\\\ACPMS\\\\Polic...', 'NPA\\\\ACPMS\\\\Model')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
